import asyncio
import logging
import os
from datetime import datetime
from typing import List, Optional
from dataclasses import dataclass

import click
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.text import Text
from rich.prompt import Prompt
from rich.markdown import Markdown
from dotenv import load_dotenv
from pydantic import BaseModel
from pydantic_ai import Agent, RunContext

from graphiti_core import Graphiti
from graphiti_core.search.search_config_recipes import NODE_HYBRID_SEARCH_RRF
from graphiti_core.llm_client.openai_generic_client import OpenAIGenericClient
from graphiti_core.cross_encoder.openai_reranker_client import OpenAIRerankerClient
from graphiti_core.llm_client.config import LLMConfig
from graphiti_core.embedder.gemini import GeminiEmbedder, GeminiEmbedderConfig
from graphiti_core.driver.kuzu_driver import KuzuDriver

# Configure logging
logging.basicConfig(
    level=logging.WARNING,  # Reduce noise in CLI
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
)
logger = logging.getLogger(__name__)

load_dotenv()

# Initialize Rich console
console = Console()

# Environment variables
OPENAI_BASE_URL = os.environ.get('OPENROUTER_BASE_URL')
OPENAI_API_KEY = os.environ.get('OPENROUTER_API_KEY')
GEMINI_API_KEY = os.environ.get('GEMINI_API_KEY')
KUZU_DB_PATH = os.environ.get('KUZU_DB_PATH', 'episodic-memory/graph_db/kuzu.db')

# Initialize clients
llm_client = OpenAIGenericClient(
    config=LLMConfig(
        api_key=OPENAI_API_KEY,
        base_url=OPENAI_BASE_URL,
        model="openai/gpt-4.1-mini",
    )
)

embedder = GeminiEmbedder(
    config=GeminiEmbedderConfig(
        api_key=GEMINI_API_KEY,
        embedding_model="embedding-001"
    )
)

cross_encoder = OpenAIRerankerClient(
    config=LLMConfig(
        api_key=OPENAI_API_KEY,
        base_url=OPENAI_BASE_URL,
        model="openai/gpt-4.1-nano",
    )
)

kuzu_driver = KuzuDriver(db=KUZU_DB_PATH)


# Pydantic models for structured responses
class SearchResult(BaseModel):
    fact: str
    uuid: str
    valid_at: Optional[str] = None
    invalid_at: Optional[str] = None


class NodeResult(BaseModel):
    name: str
    summary: str
    labels: List[str]
    uuid: str
    created_at: str


@dataclass
class SearchContext:
    """Context for search operations"""
    query: str
    results: List[SearchResult]
    nodes: List[NodeResult]


class AnswerResponse(BaseModel):
    """Structured response from the AI agent"""
    answer: str
    confidence: str  # "high", "medium", "low"
    key_points: List[str]
    limitations: Optional[str] = None


# Initialize PydanticAI agent with OpenRouter configuration
from pydantic_ai.models.openai import OpenAIChatModel
from pydantic_ai.providers.openai import OpenAIProvider

# Create OpenAI provider with custom configuration for OpenRouter
openai_provider = OpenAIProvider(
    base_url=OPENAI_BASE_URL,
    api_key=OPENAI_API_KEY,
)

# Create OpenAI model with custom provider
openai_model = OpenAIChatModel(
    model_name='openai/gpt-4o-mini',
    provider=openai_provider,
)

research_agent = Agent(
    model=openai_model,
    deps_type=SearchContext,
    output_type=AnswerResponse,
    system_prompt="""You are a carbon regulation research assistant. Your role is to analyze search results from a knowledge base of carbon regulation articles and provide comprehensive, accurate answers.

Guidelines:
1. Base your answers ONLY on the provided search results
2. Be specific and cite relevant facts when possible
3. If information is limited, acknowledge this in your limitations
4. Provide key points as bullet-style insights
5. Rate your confidence based on the quality and quantity of available evidence
6. Use clear, professional language suitable for researchers and policymakers

Remember: You are analyzing real research data about carbon regulations, LNG emissions, corporate sustainability initiatives, and related topics."""
)


class CarbonResearchQuery:
    """Enhanced query system for carbon regulation research with AI assistance."""

    def __init__(self):
        self.graphiti = None

    async def initialize(self):
        """Initialize the Graphiti connection."""
        self.graphiti = Graphiti(
            graph_driver=kuzu_driver,
            llm_client=llm_client,
            embedder=embedder,
            cross_encoder=cross_encoder,
        )
        # Build indices if needed (safe to call multiple times)
        await self.graphiti.build_indices_and_constraints()

    async def close(self):
        """Close the Graphiti connection."""
        if self.graphiti:
            await self.graphiti.close()

    async def search_knowledge_base(self, query: str, search_type: str = "facts") -> SearchContext:
        """Search the knowledge base and return structured results."""
        results = []
        nodes = []

        try:
            if search_type == "facts":
                # Search for relationships/facts
                raw_results = await self.graphiti.search(query)
                for result in raw_results:
                    results.append(SearchResult(
                        fact=result.fact,
                        uuid=result.uuid,
                        valid_at=str(result.valid_at) if hasattr(result, 'valid_at') and result.valid_at else None,
                        invalid_at=str(result.invalid_at) if hasattr(result, 'invalid_at') and result.invalid_at else None
                    ))

            elif search_type == "nodes":
                # Search for entities/nodes
                node_search_config = NODE_HYBRID_SEARCH_RRF.model_copy(deep=True)
                node_search_config.limit = 10

                raw_results = await self.graphiti._search(
                    query=query,
                    config=node_search_config,
                )

                for node in raw_results.nodes:
                    nodes.append(NodeResult(
                        name=node.name,
                        summary=node.summary,
                        labels=node.labels,
                        uuid=node.uuid,
                        created_at=str(node.created_at)
                    ))

            elif search_type == "detailed":
                # Detailed search with reranking
                initial_results = await self.graphiti.search(query)
                if initial_results:
                    center_node_uuid = initial_results[0].source_node_uuid
                    reranked_results = await self.graphiti.search(
                        query, center_node_uuid=center_node_uuid
                    )
                    for result in reranked_results:
                        results.append(SearchResult(
                            fact=result.fact,
                            uuid=result.uuid,
                            valid_at=str(result.valid_at) if hasattr(result, 'valid_at') and result.valid_at else None,
                            invalid_at=str(result.invalid_at) if hasattr(result, 'invalid_at') and result.invalid_at else None
                        ))

        except Exception as e:
            console.print(f"[red]Error during search: {e}[/red]")

        return SearchContext(query=query, results=results, nodes=nodes)

    async def get_ai_answer(self, search_context: SearchContext) -> AnswerResponse:
        """Get AI-powered answer based on search results."""
        try:
            result = await research_agent.run(
                f"Please analyze the search results and answer the question: {search_context.query}",
                deps=search_context
            )
            return result.output
        except Exception as e:
            console.print(f"[red]Error getting AI answer: {e}[/red]")
            return AnswerResponse(
                answer="I encountered an error while processing your question.",
                confidence="low",
                key_points=[],
                limitations="Technical error occurred during analysis."
            )

    def display_answer(self, answer: AnswerResponse, search_context: SearchContext):
        """Display the AI answer with rich formatting."""
        # Main answer panel
        answer_panel = Panel(
            Markdown(answer.answer),
            title="🤖 AI Analysis",
            border_style="blue",
            padding=(1, 2)
        )
        console.print(answer_panel)

        # Confidence and key points
        confidence_color = {
            "high": "green",
            "medium": "yellow",
            "low": "red"
        }.get(answer.confidence, "white")

        console.print(f"\n[bold]Confidence:[/bold] [{confidence_color}]{answer.confidence.upper()}[/{confidence_color}]")

        if answer.key_points:
            console.print("\n[bold]Key Points:[/bold]")
            for point in answer.key_points:
                console.print(f"  • {point}")

        if answer.limitations:
            console.print(f"\n[bold yellow]Limitations:[/bold yellow] {answer.limitations}")

    def display_sources(self, search_context: SearchContext):
        """Display search results as sources."""
        if search_context.results:
            console.print("\n[bold]📚 Sources - Facts & Relationships:[/bold]")

            table = Table(show_header=True, header_style="bold magenta")
            table.add_column("Fact", style="cyan", width=60)
            table.add_column("Valid From", style="green", width=15)
            table.add_column("UUID", style="dim", width=20)

            for result in search_context.results[:10]:  # Limit to 10 for display
                table.add_row(
                    result.fact[:200] + "..." if len(result.fact) > 200 else result.fact,
                    result.valid_at or "N/A",
                    result.uuid[:8] + "..."
                )

            console.print(table)

        if search_context.nodes:
            console.print("\n[bold]�️  Sources - Entities & Nodes:[/bold]")

            table = Table(show_header=True, header_style="bold magenta")
            table.add_column("Entity", style="cyan", width=20)
            table.add_column("Summary", style="white", width=50)
            table.add_column("Labels", style="yellow", width=15)

            for node in search_context.nodes[:10]:  # Limit to 10 for display
                table.add_row(
                    node.name,
                    node.summary[:150] + "..." if len(node.summary) > 150 else node.summary,
                    ", ".join(node.labels)
                )

            console.print(table)

    async def query_with_ai(self, question: str, search_type: str = "facts") -> None:
        """Main query function that combines search and AI analysis."""
        with console.status(f"[bold green]Searching knowledge base for: {question}"):
            search_context = await self.search_knowledge_base(question, search_type)

        if not search_context.results and not search_context.nodes:
            console.print("[red]❌ No results found in the knowledge base.[/red]")
            return

        with console.status("[bold green]Analyzing results with AI..."):
            answer = await self.get_ai_answer(search_context)

        # Display results
        console.print("\n" + "="*80)
        self.display_answer(answer, search_context)
        console.print("\n" + "="*80)
        self.display_sources(search_context)
        console.print("="*80)

# Add PydanticAI tool for the research agent
@research_agent.tool
async def search_knowledge_base_tool(ctx: RunContext[SearchContext], search_type: str = "facts") -> str:
    """Search the knowledge base for relevant information."""
    search_context = ctx.deps

    if search_type == "facts" and search_context.results:
        facts = []
        for result in search_context.results[:10]:
            fact_info = f"Fact: {result.fact}"
            if result.valid_at:
                fact_info += f" (Valid from: {result.valid_at})"
            facts.append(fact_info)
        return "Search Results - Facts:\n" + "\n".join(facts)

    elif search_type == "nodes" and search_context.nodes:
        nodes = []
        for node in search_context.nodes[:10]:
            node_info = f"Entity: {node.name} - {node.summary[:200]}"
            nodes.append(node_info)
        return "Search Results - Entities:\n" + "\n".join(nodes)

    return "No relevant information found in the knowledge base."


# Initialize the query system
query_system = CarbonResearchQuery()


# CLI Commands using Click
@click.group(invoke_without_command=True)
@click.pass_context
def cli(ctx):
    """🧠 Carbon Regulation Research - AI-Powered Query Tool

    Ask intelligent questions about carbon regulation articles in your knowledge base.
    The AI will search the knowledge base and provide comprehensive answers with sources.
    """
    if ctx.invoked_subcommand is None:
        # Interactive mode
        asyncio.run(interactive_mode())


@cli.command()
@click.argument('question', nargs=-1, required=True)
@click.option('--search-type', '-t', default='facts',
              type=click.Choice(['facts', 'nodes', 'detailed']),
              help='Type of search to perform')
def ask(question, search_type):
    """Ask a question about carbon regulation research.

    QUESTION: Your research question (can be multiple words)
    """
    question_text = ' '.join(question)
    asyncio.run(single_query(question_text, search_type))


@cli.command()
def stats():
    """Show knowledge base statistics."""
    asyncio.run(show_statistics())


async def single_query(question: str, search_type: str):
    """Handle a single query."""
    await query_system.initialize()
    try:
        console.print(f"\n[bold blue]Question:[/bold blue] {question}")
        await query_system.query_with_ai(question, search_type)
    finally:
        await query_system.close()


async def show_statistics():
    """Show database statistics."""
    await query_system.initialize()
    try:
        with console.status("[bold green]Gathering statistics..."):
            # Get some basic stats
            search_context = await query_system.search_knowledge_base("carbon OR emissions OR LNG OR regulation", "facts")
            node_context = await query_system.search_knowledge_base("carbon OR emissions OR LNG OR regulation", "nodes")

        console.print("\n[bold]📊 Knowledge Base Statistics[/bold]")
        console.print(f"📈 Total searchable facts: {len(search_context.results)}")
        console.print(f"🏷️  Total entities: {len(node_context.nodes)}")
        console.print(f"📅 Database path: {KUZU_DB_PATH}")

    except Exception as e:
        console.print(f"[red]Error getting stats: {e}[/red]")
    finally:
        await query_system.close()


async def interactive_mode():
    """Run the interactive CLI mode."""
    await query_system.initialize()

    try:
        # Display banner
        banner = Panel(
            "[bold blue]🧠 Carbon Regulation Research - AI Query Tool[/bold blue]\n\n"
            "Ask questions about carbon regulation articles in your knowledge base.\n"
            "The AI will search and provide comprehensive answers with sources.\n\n"
            "[dim]Commands: ask <question>, stats, help, quit[/dim]",
            title="Welcome",
            border_style="green"
        )
        console.print(banner)

        while True:
            try:
                # Get user input
                question = Prompt.ask("\n[bold cyan]💬 What would you like to know?[/bold cyan]")

                if not question.strip():
                    continue

                # Handle special commands
                if question.lower() in ['quit', 'exit', 'q']:
                    console.print("\n[bold green]👋 Goodbye![/bold green]")
                    break

                elif question.lower() in ['help', 'h']:
                    console.print("\n[bold]Available commands:[/bold]")
                    console.print("• Just type your question naturally")
                    console.print("• 'stats' - Show database statistics")
                    console.print("• 'quit' or 'exit' - Leave the application")
                    console.print("\n[bold]Example questions:[/bold]")
                    console.print("• What are the key findings about LNG emissions?")
                    console.print("• Tell me about Rystad Energy's research")
                    console.print("• What regulations affect carbon accounting?")
                    continue

                elif question.lower() == 'stats':
                    await show_statistics()
                    continue

                # Process the question with AI
                await query_system.query_with_ai(question, "facts")

            except KeyboardInterrupt:
                console.print("\n\n[bold green]👋 Goodbye![/bold green]")
                break
            except EOFError:
                console.print("\n\n[bold green]👋 Goodbye![/bold green]")
                break
            except Exception as e:
                console.print(f"[red]❌ Error: {e}[/red]")

    finally:
        await query_system.close()


if __name__ == '__main__':
    cli()